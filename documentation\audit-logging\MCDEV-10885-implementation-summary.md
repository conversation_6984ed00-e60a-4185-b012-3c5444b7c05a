# MCDEV-10885: Registration and Recurring Events Audit Logging Implementation

**Implementation Date**: October 2025  
**Dependencies**: MCDEV-10869 (Core Infrastructure), MCDEV-10764 (Event Details), MCDEV-10887 (Calendar Changes)

## Overview

This implementation extends the Event Audit Logging system to cover three additional areas:
1. **Registration Creation**: Audit logging for `insertRegistration` function and `ev_createRegistration` stored procedure
2. **Recurring Events Creation**: Audit logging for recurring event series creation in `ev_queueCreatingRecurringEvents` stored procedure
3. **Recurring Events Updates**: Audit logging for updating upcoming recurring events in `ev_updateUpcomingRecurringEvents` stored procedure

## Part 1: Registration Creation Audit Logging

### Implementation Details

**Location**: `database/membercentral/schema/memberCentral/procedures/ev_createRegistration.sql`

**Changes Made**:
- Added audit logging variables: `@orgID`, `@calendarID`, `@eventTitle`, `@registrationTypeName`, `@msgjson`
- Enhanced site query to retrieve `orgID` alongside existing fields
- Added queries to get calendar ID, event title, and registration type name for meaningful audit messages
- Added audit logging after registration creation but before transaction commit
- Uses areaCode 'REGISTRATION' to distinguish from other event operations

**Audit Message Format**:
```
Registration setup created for event [EventTitle] with type [RegistrationType] from [StartDate] to [EndDate].
```

**Example**:
```sql
EXEC dbo.ev_insertAuditLog 
    @orgID = 1,
    @siteID = 1,
    @areaCode = 'REGISTRATION',
    @msgjson = 'Registration setup created for event [Annual Conference] with type [Full Registration] from [2025-10-01 09:00:00] to [2025-10-15 17:00:00].',
    @evKeyMapJSON = '{ "EVENTID":12345, "CALENDARID":67890 }',
    @isImport = 0,
    @enteredByMemberID = 0;
```

### Call Sites Covered

1. **EventAdmin.cfc**: `saveEventRegDetails()` method calls `insertRegistration()`
2. **Import Operations**: `ev_importEventFromQueue` and other import procedures call `ev_createRegistration`
3. **Custom Applications**: CAAA and other custom apps that create registrations

### Data Logged

- Event context (event title, calendar information)
- Registration type (Full Registration, RSVP, etc.)
- Registration date range (start and end dates)
- Email settings (reply-to and notify emails are captured in the procedure but not logged in the message for brevity)

## Part 2: Recurring Events Audit Logging

### Implementation Details

**Location**: `database/membercentral/schema/memberCentral/procedures/ev_queueCreatingRecurringEvents.sql`

**Changes Made**:
- Added audit logging variables: `@orgID`, `@calendarID`, `@eventTitle`, `@formulaDesc`, `@msgjson`
- Added queries to get event context and advance formula description
- Added audit logging after recurring series creation but before transaction commit
- Uses areaCode 'EVENT' to maintain consistency with other event operations
- Logs the number of events that will be created in the series

**Audit Message Format**:
```
Recurring event series created for event [EventTitle] with pattern [FormulaDescription] ending [EndDate], creating [Count] events queued for import.
```

**Example**:
```sql
EXEC dbo.ev_insertAuditLog 
    @orgID = 1,
    @siteID = 1,
    @areaCode = 'EVENT',
    @msgjson = 'Recurring event series created for event [Weekly Training] with pattern [Weekly] ending [2025-12-31 23:59:59], creating [12] events queued for import.',
    @evKeyMapJSON = '{ "EVENTID":12345, "CALENDARID":67890 }',
    @isImport = 0,
    @enteredByMemberID = 1001;
```

### Call Sites Covered

1. **EventAdmin.cfc**: `saveEventDetails()` method when `convertToRecurringEvent=1` (existing events)
2. **EventAdmin.cfc**: `saveEventDetails()` method when `local.isRecurringEvent=true` (new events)
3. **Other procedures**: Any procedure that calls `ev_queueCreatingRecurringEvents`

### Data Logged

- Event context (event title, calendar information)
- Recurrence pattern (from advance formula description)
- End date for the recurring series
- Number of individual events that will be created
- Series ID for tracking

## Part 3: Update Upcoming Recurring Events Audit Logging

### Implementation Details

**Location**: `database/membercentral/schema/memberCentral/procedures/ev_updateUpcomingRecurringEvents.sql`

**Changes Made**:
- Added audit logging variables: `@orgID`, `@calendarID`, `@eventTitle`, `@msgjson`
- Added queries to get event context for meaningful audit messages
- Added audit logging before recurring events import but after data preparation
- Uses areaCode 'EVENT' to maintain consistency with other event operations
- Logs the number of upcoming events that will be updated

**Audit Message Format**:
```
Upcoming recurring events updated for event [EventTitle], updating [Count] future events in the series.
```

**Example**:
```sql
EXEC dbo.ev_insertAuditLog
    @orgID = 1,
    @siteID = 1,
    @areaCode = 'EVENT',
    @msgjson = 'Upcoming recurring events updated for event [Weekly Training], updating [8] future events in the series.',
    @evKeyMapJSON = '{ "EVENTID":12345, "CALENDARID":67890 }',
    @isImport = 0,
    @enteredByMemberID = 1001;
```

### Call Sites Covered

1. **EventAdmin.cfc**: `saveEventDetails()` method when `updateUpcomingRecurringEvents=1`
2. **Other procedures**: Any procedure that calls `ev_updateUpcomingRecurringEvents`

### Data Logged

- Event context (source event title, calendar information)
- Number of upcoming events that will be updated
- Series context for tracking related changes

## Infrastructure Integration

### Follows Established Patterns

- **eventKeyMapJSON**: Uses `{ "EVENTID":123, "CALENDARID":456 }` format consistently
- **Area Codes**: 'REGISTRATION' for registration operations, 'EVENT' for recurring events
- **Error Handling**: Standard TRY/CATCH blocks with `up_MCErrorHandler`
- **Message Formatting**: Uses `STRING_ESCAPE` for JSON-safe messages
- **MongoDB Integration**: Integrates with existing `auditLog_EV` collection

### Dependencies

- `ev_insertAuditLog` stored procedure (MCDEV-10869)
- `auditLog_EV.cfc` MongoDB model (MCDEV-10869)
- Queue-based processing via `platformQueue.dbo.queue_mongo`
- Event audit log admin interface (MCDEV-10873)

## Key Features

### Registration Creation Logging
- Captures all registration creation contexts (admin, import, custom apps)
- Provides meaningful context about registration type and dates
- Distinguishes between different registration types (Full, RSVP, etc.)
- Maintains backward compatibility with existing registration creation flows

### Recurring Events Logging
- Logs series creation with pattern information
- Includes count of events that will be created
- Tracks advance formula descriptions for meaningful audit messages
- Logs updates to upcoming events in existing series
- Integrates with existing recurring events workflow

### Performance Considerations
- Audit logging only occurs when new registrations/series are created
- Uses efficient queries to gather audit context data
- Minimal impact on existing registration and recurring events performance
- Conditional logging (only when required data is available)

## Testing Scenarios

### Registration Creation
1. Create registration through Event Admin interface
2. Import events with registration setup
3. Custom application registration creation
4. Different registration types (Full, RSVP, etc.)
5. Various date ranges and email configurations

### Recurring Events
1. Convert existing event to recurring series
2. Create new recurring event with different patterns
3. Different advance formulas (daily, weekly, monthly, custom)
4. Various end dates and series lengths
5. Update upcoming recurring events in an existing series
6. Error scenarios (invalid patterns, date ranges)

## Files Created/Modified

1. `database/membercentral/migrations/2025/2025-10/MCDEV-10885 - Implement Registration and Recurring Events Audit Logging.sql` - Migration script
2. `database/membercentral/schema/memberCentral/procedures/ev_createRegistration.sql` - Updated with audit logging
3. `database/membercentral/schema/memberCentral/procedures/ev_queueCreatingRecurringEvents.sql` - Updated with audit logging
4. `database/membercentral/schema/memberCentral/procedures/ev_updateUpcomingRecurringEvents.sql` - Updated with audit logging
5. `documentation/audit-logging/MCDEV-10885-implementation-summary.md` - This documentation

## Integration with Existing Audit System

### Admin Interface Integration
- Registration audit logs appear in Event Audit Log admin interface with 'REGISTRATION' area code filter
- Recurring events audit logs appear with 'EVENT' area code filter alongside other event operations
- Consistent filtering and search capabilities
- Same date range and keyword search functionality

### MongoDB Collection Structure
- Uses existing `auditLog_EV` collection in `membercentralAudit` database
- Maintains consistent document structure with other event audit logs
- Supports existing query patterns and indexing strategies
- Compatible with current audit log processing and archival procedures

**Implementation Complete**: Registration creation and recurring events audit logging is now fully integrated with the existing Event Audit Logging system.
